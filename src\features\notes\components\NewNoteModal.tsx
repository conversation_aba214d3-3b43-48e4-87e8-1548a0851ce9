import React, { useState, useRef, useEffect } from 'react';
import { FiX, FiPlus, FiTag } from 'react-icons/fi';

interface NewNoteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateNote: (content: string, tags: string[]) => void;
  availableTags: string[];
}

const NewNoteModal: React.FC<NewNoteModalProps> = ({
  isOpen,
  onClose,
  onCreateNote,
  availableTags
}) => {
  const [content, setContent] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [showTagInput, setShowTagInput] = useState(false);
  const [showTagSuggestions, setShowTagSuggestions] = useState(false);
  const contentRef = useRef<HTMLTextAreaElement>(null);
  const tagInputRef = useRef<HTMLInputElement>(null);

  // Focus on content input when modal opens
  useEffect(() => {
    if (isOpen && contentRef.current) {
      contentRef.current.focus();
    }
  }, [isOpen]);

  // Focus on tag input when it becomes visible
  useEffect(() => {
    if (showTagInput && tagInputRef.current) {
      tagInputRef.current.focus();
    }
  }, [showTagInput]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (content.trim()) {
      onCreateNote(content.trim(), tags);
      handleClose();
    }
  };

  const handleClose = () => {
    setContent('');
    setTags([]);
    setTagInput('');
    setShowTagInput(false);
    setShowTagSuggestions(false);
    onClose();
  };

  const handleAddTag = (tag: string) => {
    const trimmedTag = tag.trim().toLowerCase();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      setTags([...tags, trimmedTag]);
    }
    setTagInput('');
    setShowTagInput(false);
    setShowTagSuggestions(false);
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleTagInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (tagInput.trim()) {
        handleAddTag(tagInput);
      }
    } else if (e.key === 'Escape') {
      setShowTagInput(false);
      setShowTagSuggestions(false);
      setTagInput('');
    }
  };

  const handleContentKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSubmit(e as any);
    }
    // Allow normal Enter key behavior for line breaks in textarea
    // Don't prevent default for regular Enter key
  };

  const handleFormKeyDown = (e: React.KeyboardEvent) => {
    // Prevent form submission on Enter key unless it's Ctrl+Enter or Cmd+Enter
    if (e.key === 'Enter' && !e.metaKey && !e.ctrlKey) {
      e.preventDefault();
    }
  };

  const filteredSuggestions = availableTags.filter(tag => 
    tag.toLowerCase().includes(tagInput.toLowerCase()) && 
    !tags.includes(tag)
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
      <div className="bg-[#191c22] rounded-xl shadow-lg w-full max-w-2xl mx-4 border border-gray-700">
        <form onSubmit={handleSubmit} onKeyDown={handleFormKeyDown}>
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-700">
            <h2 className="text-xl font-semibold text-gray-100">Create New Note</h2>
            <button
              type="button"
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-200 transition-colors"
              aria-label="Close"
            >
              <FiX size={24} />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 space-y-4">
            {/* Note Content */}
            <div>
              <label htmlFor="note-content" className="block text-sm font-medium text-gray-300 mb-2">
                Note Content
              </label>
              <textarea
                ref={contentRef}
                id="note-content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                onKeyDown={handleContentKeyDown}
                placeholder="Enter your note content..."
                className="w-full h-32 px-3 py-2 bg-[#23272f] border border-gray-600 rounded-lg text-gray-100 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                required
              />
              <p className="text-xs text-gray-400 mt-1">
                Press Ctrl+Enter (Cmd+Enter on Mac) to create note
              </p>
            </div>

            {/* Tags Section */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Tags
              </label>
              
              {/* Selected Tags */}
              <div className="flex flex-wrap gap-2 mb-3">
                {tags.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-blue-600 text-blue-100 rounded-full"
                  >
                    <FiTag size={10} />
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 hover:text-blue-200 transition-colors"
                    >
                      <FiX size={10} />
                    </button>
                  </span>
                ))}
              </div>

              {/* Add Tag Input */}
              {showTagInput ? (
                <div className="relative">
                  <input
                    ref={tagInputRef}
                    type="text"
                    value={tagInput}
                    onChange={(e) => {
                      setTagInput(e.target.value);
                      setShowTagSuggestions(e.target.value.length > 0);
                    }}
                    onKeyDown={handleTagInputKeyDown}
                    onBlur={() => {
                      setTimeout(() => {
                        setShowTagInput(false);
                        setShowTagSuggestions(false);
                        setTagInput('');
                      }, 200);
                    }}
                    placeholder="Enter tag name..."
                    className="w-full px-3 py-2 bg-[#23272f] border border-gray-600 rounded-lg text-gray-100 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  
                  {/* Tag Suggestions */}
                  {showTagSuggestions && filteredSuggestions.length > 0 && (
                    <div className="absolute top-full left-0 right-0 mt-1 bg-[#23272f] border border-gray-600 rounded-lg shadow-lg max-h-32 overflow-y-auto z-10">
                      {filteredSuggestions.map((suggestion) => (
                        <button
                          key={suggestion}
                          type="button"
                          onClick={() => handleAddTag(suggestion)}
                          className="w-full px-3 py-2 text-left text-gray-100 hover:bg-gray-700 transition-colors"
                        >
                          <FiTag size={12} className="inline mr-2" />
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => setShowTagInput(true)}
                  className="inline-flex items-center gap-1 px-3 py-2 text-sm text-gray-400 hover:text-gray-300 border border-dashed border-gray-600 hover:border-gray-500 rounded-lg transition-colors"
                >
                  <FiPlus size={14} />
                  Add tag
                </button>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-700">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-400 hover:text-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!content.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Create Note
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewNoteModal;
